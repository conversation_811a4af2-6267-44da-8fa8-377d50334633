[{"getter": {"returnArgStart": 1, "name": "GET_ENTITY_COORDS", "returnType": "Vector3"}, "hash": "0x06843DA7060A026B", "type": "ctx", "name": "SET_ENTITY_COORDS", "args": [{"type": "Entity", "translate": true}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "BOOL"}, {"type": "BOOL"}, {"type": "BOOL"}, {"type": "BOOL"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x8524A8B0171D5E07", "type": "ctx", "name": "SET_ENTITY_ROTATION", "args": [{"type": "Entity", "translate": true}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "int"}, {"type": "BOOL"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x1C99BB7B6E96D16F", "type": "ctx", "name": "SET_ENTITY_VELOCITY", "args": [{"type": "Entity", "translate": true}, {"type": "float"}, {"type": "float"}, {"type": "float"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x8E2530AA8ADA980E", "type": "ctx", "name": "SET_ENTITY_HEADING", "args": [{"type": "Entity", "translate": true}, {"type": "float"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x428CA6DBD1094446", "type": "ctx", "name": "FREEZE_ENTITY_POSITION", "args": [{"type": "Entity", "translate": true}, {"type": "BOOL"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0xC5F68BE9613E2D18", "type": "ctx", "name": "APPLY_FORCE_TO_ENTITY", "args": [{"type": "Entity", "translate": true}, {"type": "int"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "int"}, {"type": "BOOL"}, {"type": "BOOL"}, {"type": "BOOL"}, {"type": "BOOL"}, {"type": "BOOL"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x00A1CADD00108836", "type": "ctx", "name": "SET_PLAYER_MODEL", "args": [{"type": "Player", "translate": true}, {"type": "Hash"}], "ctx": {"type": "Player", "idx": 0}}, {"hash": "0x8D32347D6D4C40A2", "type": "ctx", "name": "SET_PLAYER_CONTROL", "args": [{"type": "Player", "translate": true}, {"type": "BOOL"}, {"type": "int"}], "ctx": {"type": "Player", "idx": 0}}, {"hash": "0x239528EACDC3E7DE", "type": "ctx", "name": "SET_PLAYER_INVINCIBLE", "args": [{"type": "Player", "translate": true}, {"type": "BOOL"}], "ctx": {"type": "Player", "idx": 0}}, {"hash": "0x39FF19C64EF7DA5B", "type": "ctx", "name": "SET_PLAYER_WANTED_LEVEL", "args": [{"type": "Player", "translate": true}, {"type": "int"}, {"type": "BOOL"}], "ctx": {"type": "Player", "idx": 0}}, {"hash": "0xB302540597885499", "type": "ctx", "name": "CLEAR_PLAYER_WANTED_LEVEL", "args": [{"type": "Player", "translate": true}], "ctx": {"type": "Player", "idx": 0}}, {"type": "entity", "hash": "0xD49F9B0955C367DE", "args": [{"type": "int"}, {"type": "Hash"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "BOOL"}, {"type": "BOOL"}], "name": "CREATE_PED"}, {"type": "entity", "hash": "0x7DD959874C1FD534", "args": [{"type": "Entity", "translate": true}, {"type": "int"}, {"type": "Hash"}, {"type": "int"}, {"type": "BOOL"}, {"type": "BOOL"}], "name": "CREATE_PED_INSIDE_VEHICLE"}, {"hash": "0x5F5D1665E352A839", "type": "ctx", "name": "ADD_PED_DECORATION_FROM_HASHES", "args": [{"type": "Entity", "translate": true}, {"type": "Hash"}, {"type": "Hash"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0xF75B0D629E1C063D", "type": "ctx", "name": "SET_PED_INTO_VEHICLE", "args": [{"type": "Entity", "translate": true}, {"type": "Entity", "translate": true}, {"type": "int"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x9414E18B9434C2FE", "type": "ctx", "name": "SET_PED_HEAD_BLEND_DATA", "args": [{"type": "Entity", "translate": true}, {"type": "int"}, {"type": "int"}, {"type": "int"}, {"type": "int"}, {"type": "int"}, {"type": "int"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "BOOL"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x48F44967FA05CC1E", "type": "ctx", "name": "SET_PED_HEAD_OVERLAY", "args": [{"type": "Entity", "translate": true}, {"type": "int"}, {"type": "int"}, {"type": "float"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x4CFFC65454C93A49", "type": "ctx", "name": "SET_PED_HAIR_TINT", "args": [{"type": "Entity", "translate": true}, {"type": "int"}, {"type": "int"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x497BF74A7B9CB952", "type": "ctx", "name": "_SET_PED_HEAD_OVERLAY_COLOR", "args": [{"type": "Entity", "translate": true}, {"type": "int"}, {"type": "int"}, {"type": "int"}, {"type": "int"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x50B56988B170AFDF", "type": "ctx", "name": "_SET_PED_EYE_COLOR", "args": [{"type": "Entity", "translate": true}, {"type": "int"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x71A5C1DBA060049E", "type": "ctx", "name": "_SET_PED_FACE_FEATURE", "args": [{"type": "Entity", "translate": true}, {"type": "int"}, {"type": "float"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x45EEE61580806D63", "type": "ctx", "name": "SET_PED_DEFAULT_COMPONENT_VARIATION", "args": [{"type": "Entity", "translate": true}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0xC8A9481A01E63C28", "type": "ctx", "name": "SET_PED_RANDOM_COMPONENT_VARIATION", "args": [{"type": "Entity", "translate": true}, {"type": "int"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x262B14F48D29DE80", "type": "ctx", "name": "SET_PED_COMPONENT_VARIATION", "args": [{"type": "Entity", "translate": true}, {"type": "int"}, {"type": "int"}, {"type": "int"}, {"type": "int"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x0943E5B8E078E76E", "type": "ctx", "name": "CLEAR_PED_PROP", "args": [{"type": "Entity", "translate": true}, {"type": "int"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0xC44AA05345C992C6", "type": "ctx", "name": "SET_PED_RANDOM_PROPS", "args": [{"type": "Entity", "translate": true}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x93376B65A266EB5F", "type": "ctx", "name": "SET_PED_PROP_INDEX", "args": [{"type": "Entity", "translate": true}, {"type": "int"}, {"type": "int"}, {"type": "int"}, {"type": "BOOL"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0xCEA04D83135264CC", "type": "ctx", "name": "SET_PED_ARMOUR", "args": [{"type": "Entity", "translate": true}, {"type": "int"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0xB128377056A54E2A", "type": "ctx", "name": "SET_PED_CAN_RAGDOLL", "args": [{"type": "Entity", "translate": true}, {"type": "BOOL"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x1913FE4CBF41C463", "type": "ctx", "name": "SET_PED_CONFIG_FLAG", "args": [{"type": "Entity", "translate": true}, {"type": "int"}, {"type": "BOOL"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0xC1E8A365BF3B29F2", "type": "ctx", "name": "SET_PED_RESET_FLAG", "args": [{"type": "Entity", "translate": true}, {"type": "int"}, {"type": "BOOL"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0xAE99FB955581844A", "type": "ctx", "name": "SET_PED_TO_RAGDOLL", "args": [{"type": "Entity", "translate": true}, {"type": "int"}, {"type": "int"}, {"type": "int"}, {"type": "BOOL"}, {"type": "BOOL"}, {"type": "BOOL"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0xD76632D99E4966C8", "type": "ctx", "name": "SET_PED_TO_RAGDOLL_WITH_FALL", "args": [{"type": "Entity", "translate": true}, {"type": "int"}, {"type": "int"}, {"type": "int"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0xBF0FD6E56C964FCB", "type": "ctx", "name": "GIVE_WEAPON_TO_PED", "args": [{"type": "Entity", "translate": true}, {"type": "Hash"}, {"type": "int"}, {"type": "BOOL"}, {"type": "BOOL"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0xD966D51AA5B28BB9", "type": "ctx", "name": "GIVE_WEAPON_COMPONENT_TO_PED", "args": [{"type": "Entity", "translate": true}, {"type": "Hash"}, {"type": "Hash"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x4899CB088EDF59B8", "type": "ctx", "name": "REMOVE_WEAPON_FROM_PED", "args": [{"type": "Entity", "translate": true}, {"type": "Hash"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0xF25DF915FA38C5F3", "type": "ctx", "name": "REMOVE_ALL_PED_WEAPONS", "args": [{"type": "Entity", "translate": true}, {"type": "BOOL"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x1E8BE90C74FB4C09", "type": "ctx", "name": "REMOVE_WEAPON_COMPONENT_FROM_PED", "args": [{"type": "Entity", "translate": true}, {"type": "Hash"}, {"type": "Hash"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0xADF692B254977C0C", "type": "ctx", "name": "SET_CURRENT_PED_WEAPON", "args": [{"type": "Entity", "translate": true}, {"type": "Hash"}, {"type": "BOOL"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x14E56BC5B5DB6A19", "type": "ctx", "name": "SET_PED_AMMO", "args": [{"type": "Entity", "translate": true}, {"type": "Hash"}, {"type": "int"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x72C896464915D1B1", "type": "ctx", "name": "TASK_REACT_AND_FLEE_PED", "args": [{"type": "Entity", "translate": true}, {"type": "Entity", "translate": true}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x46A6CC01E0826106", "type": "ctx", "name": "TASK_SHOOT_AT_COORD", "args": [{"type": "Entity", "translate": true}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "int"}, {"type": "Hash"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x08DA95E8298AE772", "type": "ctx", "name": "TASK_SHOOT_AT_ENTITY", "args": [{"type": "Entity", "translate": true}, {"type": "Entity", "translate": true}, {"type": "int"}, {"type": "Hash"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0xF166E48407BAC484", "type": "ctx", "name": "TASK_COMBAT_PED", "args": [{"type": "Entity", "translate": true}, {"type": "Entity", "translate": true}, {"type": "int"}, {"type": "int"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x2F8AF0E82773A171", "type": "ctx", "name": "TASK_DRIVE_BY", "args": [{"type": "Entity", "translate": true}, {"type": "Entity", "translate": true}, {"type": "Entity", "translate": true}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "int"}, {"type": "BOOL"}, {"type": "Hash"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0xC20E50AA46D09CA8", "type": "ctx", "name": "TASK_ENTER_VEHICLE", "args": [{"type": "Entity", "translate": true}, {"type": "Entity", "translate": true}, {"type": "int"}, {"type": "int"}, {"type": "float"}, {"type": "int"}, {"type": "Any"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x9A7D091411C5F684", "type": "ctx", "name": "TASK_WARP_PED_INTO_VEHICLE", "args": [{"type": "Entity", "translate": true}, {"type": "Entity", "translate": true}, {"type": "int"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0xF2EAB31979A7F910", "type": "ctx", "name": "TASK_HANDS_UP", "args": [{"type": "Entity", "translate": true}, {"type": "int"}, {"type": "Entity", "translate": true}, {"type": "int"}, {"type": "BOOL"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0xEA47FE3719165B94", "type": "ctx", "name": "TASK_PLAY_ANIM", "args": [{"type": "Entity", "translate": true}, {"type": "char<PERSON><PERSON>"}, {"type": "char<PERSON><PERSON>"}, {"type": "float"}, {"type": "float"}, {"type": "int"}, {"type": "int"}, {"type": "float"}, {"type": "BOOL"}, {"type": "BOOL"}, {"type": "BOOL"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x83CDB10EA29B370B", "type": "ctx", "name": "TASK_PLAY_ANIM_ADVANCED", "args": [{"type": "Entity", "translate": true}, {"type": "char<PERSON><PERSON>"}, {"type": "char<PERSON><PERSON>"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "int"}, {"type": "Any"}, {"type": "float"}, {"type": "Any"}, {"type": "Any"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0xE1EF3C1216AFF2CD", "type": "ctx", "name": "CLEAR_PED_TASKS", "args": [{"type": "Entity", "translate": true}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0xAAA34F8A7CB32098", "type": "ctx", "name": "CLEAR_PED_TASKS_IMMEDIATELY", "args": [{"type": "Entity", "translate": true}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x176CECF6F920D707", "type": "ctx", "name": "CLEAR_PED_SECONDARY_TASK", "args": [{"type": "Entity", "translate": true}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x7F93691AB4B92272", "type": "ctx", "name": "TASK_EVERYONE_LEAVE_VEHICLE", "args": [{"type": "Entity", "translate": true}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x504D54DF3F6F2247", "type": "ctx", "name": "TASK_LEAVE_ANY_VEHICLE", "args": [{"type": "Entity", "translate": true}, {"type": "int"}, {"type": "int"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0xD3DBCE61A490BE02", "type": "ctx", "name": "TASK_LEAVE_VEHICLE", "args": [{"type": "Entity", "translate": true}, {"type": "Entity", "translate": true}, {"type": "int"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0xD76B57B44F1E6F8B", "type": "ctx", "name": "TASK_GO_STRAIGHT_TO_COORD", "args": [{"type": "Entity", "translate": true}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "int"}, {"type": "float"}, {"type": "float"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x5BC448CB78FA3E88", "type": "ctx", "name": "TASK_GO_TO_COORD_ANY_MEANS", "args": [{"type": "Entity", "translate": true}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "Entity", "translate": true}, {"type": "BOOL"}, {"type": "int"}, {"type": "float"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x6A071245EB0D1882", "type": "ctx", "name": "TASK_GO_TO_ENTITY", "args": [{"type": "Entity", "translate": true}, {"type": "Entity", "translate": true}, {"type": "int"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "int"}], "ctx": {"type": "Entity", "idx": 0}}, {"type": "entity", "hash": "0xAF35D0D2583051B0", "args": [{"type": "Hash"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "BOOL"}, {"type": "BOOL"}], "name": "CREATE_VEHICLE"}, {"hash": "0xCDE5E70C1DDB954C", "type": "ctx", "name": "SET_VEHICLE_ALARM", "args": [{"type": "Entity", "translate": true}, {"type": "BOOL"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0xB77D05AC8C78AADB", "type": "ctx", "name": "SET_VEHICLE_BODY_HEALTH", "args": [{"type": "Entity", "translate": true}, {"type": "float"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x4F1D4BE3A7F24601", "type": "ctx", "name": "SET_VEHICLE_COLOURS", "args": [{"type": "Entity", "translate": true}, {"type": "int"}, {"type": "int"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x33E8CD3322E2FE31", "type": "ctx", "name": "SET_VEHICLE_COLOUR_COMBINATION", "args": [{"type": "Entity", "translate": true}, {"type": "int"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x7141766F91D15BEA", "type": "ctx", "name": "SET_VEHICLE_CUSTOM_PRIMARY_COLOUR", "args": [{"type": "Entity", "translate": true}, {"type": "int"}, {"type": "int"}, {"type": "int"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x36CED73BFED89754", "type": "ctx", "name": "SET_VEHICLE_CUSTOM_SECONDARY_COLOUR", "args": [{"type": "Entity", "translate": true}, {"type": "int"}, {"type": "int"}, {"type": "int"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x79D3B596FE44EE8B", "type": "ctx", "name": "SET_VEHICLE_DIRT_LEVEL", "args": [{"type": "Entity", "translate": true}, {"type": "float"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0xD4D4F6A4AB575A33", "type": "ctx", "name": "SET_VEHICLE_DOOR_BROKEN", "args": [{"type": "Entity", "translate": true}, {"type": "int"}, {"type": "BOOL"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0x95A88F0B409CDA47", "type": "ctx", "name": "SET_VEHICLE_NUMBER_PLATE_TEXT", "args": [{"type": "Entity", "translate": true}, {"type": "char<PERSON><PERSON>"}], "ctx": {"type": "Entity", "idx": 0}}, {"hash": "0xB664292EAECF7FA6", "type": "ctx", "name": "SET_VEHICLE_DOORS_LOCKED", "args": [{"type": "Entity", "translate": true}, {"type": "int"}], "ctx": {"type": "Entity", "idx": 0}}, {"type": "entity", "hash": "0x509D5878EB39E842", "args": [{"type": "Hash"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "BOOL"}, {"type": "BOOL"}, {"type": "BOOL"}], "name": "CREATE_OBJECT"}, {"type": "entity", "hash": "0x9A294B2138ABB884", "args": [{"type": "Hash"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "BOOL"}, {"type": "BOOL"}, {"type": "BOOL"}], "name": "CREATE_OBJECT_NO_OFFSET"}, {"type": "object", "hash": "0xCE5D0E5E315DB238", "args": [{"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}], "name": "_ADD_BLIP_FOR_AREA"}, {"type": "object", "hash": "0x5A039BB0BCA604B6", "args": [{"type": "float"}, {"type": "float"}, {"type": "float"}], "name": "ADD_BLIP_FOR_COORD"}, {"type": "object", "hash": "0x46818D79B1F7499A", "args": [{"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}], "name": "ADD_BLIP_FOR_RADIUS"}, {"type": "object", "hash": "0x5CDE92C702A8FCE7", "args": [{"type": "Entity", "translate": true}], "name": "ADD_BLIP_FOR_ENTITY"}, {"getter": {"returnArgStart": 1, "name": "GET_BLIP_SPRITE", "returnType": "int"}, "hash": "0xDF735600A4696DAF", "type": "ctx", "name": "SET_BLIP_SPRITE", "args": [{"type": "ObjRef", "translate": true}, {"type": "int"}], "ctx": {"type": "ObjRef", "idx": 0}}, {"hash": "0x86A652570E5F25DD", "type": "ctx", "name": "REMOVE_BLIP", "args": [{"type": "<PERSON><PERSON>j<PERSON><PERSON>", "translate": true}], "ctx": {"type": "<PERSON><PERSON>j<PERSON><PERSON>", "idx": 0}}]